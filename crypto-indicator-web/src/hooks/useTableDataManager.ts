import { useCallback, useEffect, useMemo, useState } from 'react';

import { useChartData } from '@/hooks/useChartData';
import { useCryptoData } from '@/hooks/useCryptoData';
import { useFiltering } from '@/hooks/useFiltering';
import { useSorting } from '@/hooks/useSorting';
import { useStockChartData } from '@/hooks/useStockChartData';
import { useStockData } from '@/hooks/useStockData';
import { useStockFiltering } from '@/hooks/useStockFiltering';
import { useStockSorting } from '@/hooks/useStockSorting';
import {
  findBtcDataForSymbol,
  formatDate,
  processCryptoStatistics,
} from '@/utils/dataProcessors';
import { processStockStatistics } from '@/utils/stockDataProcessors';
import { applyStockFilters } from '@/utils/stockTableFiltering';
import { applyStockSorting } from '@/utils/stockTableSorting';
import { applyFilters } from '@/utils/tableFiltering';
import { applySorting } from '@/utils/tableSorting';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';

interface UseTableDataManagerProps {
  assetType: 'crypto' | 'stock';
}

interface TableDataManagerReturn {
  // Processed data
  processedData: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  error: string | null;

  // Chart data
  chartData: unknown;
  showChart: boolean;
  setShowChart: (show: boolean) => void;

  // Actions
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;

  // Utilities
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Unified hook that manages all table data logic for both crypto and stock
 * This replaces the massive StatisticsTable component logic
 */
// eslint-disable-next-line max-lines-per-function
export const useTableDataManager = ({
  assetType
}: UseTableDataManagerProps): TableDataManagerReturn => {
  const [showChart, setShowChart] = useState(false);

  // Crypto hooks
  const {
    data: cryptoStatistics,
    loading: cryptoLoading,
    error: cryptoError,
    fetchData: fetchCryptoData,
  } = useCryptoData();

  const {
    chartData: cryptoChartData,
    fetchChartData: fetchCryptoChartData,
  } = useChartData();

  const {
    filterConfig: cryptoFilterConfig,
  } = useFiltering();

  const {
    sortConfig: cryptoSortConfig,
  } = useSorting();

  // Stock hooks
  const {
    data: stockStatistics,
    loading: stockLoading,
    error: stockError,
    fetchData: fetchStockData,
  } = useStockData();

  const {
    chartData: stockChartData,
    fetchChartData: fetchStockChartData,
  } = useStockChartData();

  const {
    filterConfig: stockFilterConfig,
  } = useStockFiltering();

  const {
    sortConfig: stockSortConfig,
  } = useStockSorting();

  // Select data based on asset type
  const rawData = assetType === 'crypto' ? cryptoStatistics : stockStatistics;
  const loading = assetType === 'crypto' ? cryptoLoading : stockLoading;
  const error = assetType === 'crypto' ? cryptoError : stockError;
  const chartData = assetType === 'crypto' ? cryptoChartData : stockChartData;

  // Process and filter data
  const processedData = useMemo(() => {
    if (rawData === null || rawData === undefined || rawData.length === 0) {return [];}

    if (assetType === 'crypto') {
      const result = processCryptoStatistics(rawData as CryptoCurrencyStatisticsDto[]);
      return result.usdStatistics;
    }
    const result = processStockStatistics(rawData as StockStatisticsDto[]);
    return result.stockStatistics;
  }, [rawData, assetType]);

  // BTC statistics (only for crypto)
  const btcStatistics = useMemo(() => {
    if (assetType === 'crypto' && rawData !== null && rawData !== undefined && rawData.length > 0) {
      const result = processCryptoStatistics(rawData as CryptoCurrencyStatisticsDto[]);
      return result.btcStatistics;
    }
    return [];
  }, [rawData, assetType]);

  const filteredData = useMemo(() => {
    if (processedData === null || processedData === undefined || processedData.length === 0) {return [];}

    if (assetType === 'crypto') {
      const filtered = applyFilters(
        processedData as CryptoCurrencyStatisticsDto[],
        btcStatistics,
        cryptoFilterConfig,
        findBtcDataForSymbol
      );
      return applySorting(filtered, btcStatistics, cryptoSortConfig, findBtcDataForSymbol);
    }
    const filtered = applyStockFilters(
      processedData as StockStatisticsDto[],
      stockFilterConfig
    );
    return applyStockSorting(filtered, stockSortConfig);

  }, [
    processedData,
    assetType,
    btcStatistics,
    cryptoFilterConfig,
    cryptoSortConfig,
    stockFilterConfig,
    stockSortConfig
  ]);

  // Signal click handler
  const onSignalClick = useCallback(async (symbol: string, currency: string) => {
    try {
      const fetchFunction = assetType === 'crypto' ? fetchCryptoChartData : fetchStockChartData;
      await fetchFunction(symbol, currency);
      setShowChart(true);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to handle signal click:', error);
    }
  }, [assetType, fetchCryptoChartData, fetchStockChartData]);

  // Refresh handler
  const onRefresh = useCallback(() => {
    if (assetType === 'crypto') {
      void fetchCryptoData();
    } else {
      void fetchStockData();
    }
  }, [assetType, fetchCryptoData, fetchStockData]);

  // Initial data fetch effect
  useEffect(() => {
    onRefresh();
  }, [onRefresh]);

  // Auto-refresh effect
  useEffect(() => {
    const REFRESH_INTERVAL = 30_000; // 30 seconds
    const interval = setInterval(() => {
      onRefresh();
    }, REFRESH_INTERVAL);

    return () => { clearInterval(interval); };
  }, [onRefresh]);

  return {
    // Processed data
    processedData: filteredData,
    btcStatistics,
    totalCount: processedData.length,
    filteredCount: filteredData.length,
    loading,
    error,
    
    // Chart data
    chartData: chartData as unknown,
    showChart,
    setShowChart,
    
    // Actions
    onSignalClick,
    onRefresh,
    
    // Utilities
    formatDate,
    findBtcDataForSymbol,
  };
};
