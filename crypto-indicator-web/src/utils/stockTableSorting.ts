import type { StockIndicatorValueDto, StockStatisticsDto } from "@/generated";
import type { StockSortColumn,StockSortConfig } from "@/types/table";
import { findBtcDataForSymbol } from "@/utils/dataProcessors";
import { getLatestStockData } from "./stockDataProcessors";

/**
 * Get sort value for a stock based on column
 */
const getSortValue = (
  stock: StockStatisticsDto,
  column: StockSortColumn,
  latestData: StockIndicatorValueDto | undefined,
  btcStatistics: StockStatisticsDto[],
): string | number => {
  switch (column) {
    case 'symbol': {
      return stock.symbol;
    }
    case 'usdPrice': {
      return latestData?.close ?? 0;
    }
    case 'marketCap': {
      return latestData?.volume ?? 0;
    }
    case 'usdSignal': {
      // Sort by signal color: gold > blue > gray
      const signalOrder = { gold: 3, blue: 2, gray: 1 };
      return signalOrder[latestData?.color as keyof typeof signalOrder] ?? 0;
    }
    case 'btcPrice': {
      const btcData = findBtcDataForSymbol(btcStatistics, stock.symbol);
      return btcData?.close ?? 0;
    }
    case 'btcSignal': {
      const btcData = findBtcDataForSymbol(btcStatistics, stock.symbol);
      const signalOrder = { gold: 3, blue: 2, gray: 1 };
      return signalOrder[btcData?.color as keyof typeof signalOrder] ?? 0;
    }
    default: {
      return '';
    }
  }
};

/**
 * Compare two values for sorting
 */
const compareValues = (
  aValue: string | number,
  bValue: string | number,
  direction: 'asc' | 'desc',
): number => {
  if (typeof aValue === 'string' && typeof bValue === 'string') {
    const comparison = aValue.localeCompare(bValue);
    return direction === 'asc' ? comparison : -comparison;
  }

  if (typeof aValue === 'number' && typeof bValue === 'number') {
    const comparison = aValue - bValue;
    return direction === 'asc' ? comparison : -comparison;
  }

  return 0;
};

/**
 * Apply sorting to stock data
 */
export const applyStockSorting = (
  data: StockStatisticsDto[],
  btcStatistics: StockStatisticsDto[],
  sortConfig: StockSortConfig,
): StockStatisticsDto[] => {
  if (!sortConfig.column || !sortConfig.direction) {
    return data;
  }

  return [...data].sort((a, b) => {
    const aData = getLatestStockData(a);
    const bData = getLatestStockData(b);

    if (!sortConfig.column || !sortConfig.direction) {
      return 0;
    }

    const aValue = getSortValue(a, sortConfig.column, aData, btcStatistics);
    const bValue = getSortValue(b, sortConfig.column, bData, btcStatistics);

    return compareValues(aValue, bValue, sortConfig.direction);
  });
};
